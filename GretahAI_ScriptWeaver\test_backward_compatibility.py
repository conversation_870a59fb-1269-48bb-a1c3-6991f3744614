#!/usr/bin/env python3
"""
Test backward compatibility with existing StateManager instances.

This script tests that the new error handling works with StateManager instances
that don't have the new error handling methods (simulating existing sessions).
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

def test_safe_functions_with_old_state():
    """Test safe functions with a StateManager that doesn't have new methods."""
    print("Testing backward compatibility with old StateManager...")
    
    # Create a mock old StateManager (without new error handling methods)
    class OldStateManager:
        def __init__(self):
            self.selected_step = {'Step No': '1', 'Test Steps': 'Click button', 'Expected Result': 'Page loads'}
            self.generated_script_path = '/path/to/script.py'
            # Note: No error handling fields or methods
    
    old_state = OldStateManager()
    
    # Import the safe functions
    from stages.stage7 import _has_unacknowledged_error, _set_execution_error_safe, _acknowledge_execution_error_safe, _clear_execution_error_safe
    
    # Test _has_unacknowledged_error with old state
    print(f"Initial unacknowledged error check: {_has_unacknowledged_error(old_state)}")
    assert not _has_unacknowledged_error(old_state), "Should return False for old state without error fields"
    
    # Test _set_execution_error_safe with old state
    error_details = {
        'error_message': 'Test execution failed',
        'returncode': 1,
        'stdout': 'Test output',
        'stderr': 'Test error',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '1',
        'script_path': '/path/to/script.py'
    }
    
    _set_execution_error_safe(old_state, error_details)
    
    # Check that fields were added
    assert hasattr(old_state, 'execution_error_occurred'), "Should add execution_error_occurred field"
    assert hasattr(old_state, 'execution_error_acknowledged'), "Should add execution_error_acknowledged field"
    assert hasattr(old_state, 'execution_error_details'), "Should add execution_error_details field"
    
    assert old_state.execution_error_occurred == True, "Should set error occurred to True"
    assert old_state.execution_error_acknowledged == False, "Should set acknowledged to False"
    assert old_state.execution_error_details == error_details, "Should store error details"
    
    print("✓ Safe error setting works with old StateManager")
    
    # Test _has_unacknowledged_error after setting error
    assert _has_unacknowledged_error(old_state), "Should return True after setting error"
    print("✓ Error detection works after setting error")
    
    # Test _acknowledge_execution_error_safe
    result = _acknowledge_execution_error_safe(old_state)
    assert result == True, "Should return True when acknowledging error"
    assert old_state.execution_error_acknowledged == True, "Should set acknowledged to True"
    print("✓ Safe error acknowledgment works with old StateManager")
    
    # Test _clear_execution_error_safe
    result = _clear_execution_error_safe(old_state)
    assert result == True, "Should return True when clearing error"
    assert old_state.execution_error_occurred == False, "Should set error occurred to False"
    assert old_state.execution_error_acknowledged == False, "Should set acknowledged to False"
    assert old_state.execution_error_details == {}, "Should clear error details"
    print("✓ Safe error clearing works with old StateManager")
    
    # Test _has_unacknowledged_error after clearing
    assert not _has_unacknowledged_error(old_state), "Should return False after clearing error"
    print("✓ Error detection works after clearing error")

def test_state_upgrade():
    """Test StateManager upgrade functionality."""
    print("\nTesting StateManager upgrade functionality...")
    
    # Create a mock old StateManager
    class OldStateManager:
        def __init__(self):
            self.selected_step = {'Step No': '1'}
            self.generated_script_path = '/path/to/script.py'
    
    old_state = OldStateManager()
    
    # Import StateManager to get the upgrade method
    from state_manager import StateManager
    new_state = StateManager()
    
    # Test upgrade
    new_state._upgrade_existing_state(old_state)
    
    # Check that fields were added
    assert hasattr(old_state, 'execution_error_occurred'), "Should add execution_error_occurred field"
    assert hasattr(old_state, 'execution_error_acknowledged'), "Should add execution_error_acknowledged field"
    assert hasattr(old_state, 'execution_error_details'), "Should add execution_error_details field"
    
    # Check that methods were added
    assert hasattr(old_state, 'set_execution_error'), "Should add set_execution_error method"
    assert hasattr(old_state, 'acknowledge_execution_error'), "Should add acknowledge_execution_error method"
    assert hasattr(old_state, 'clear_execution_error'), "Should add clear_execution_error method"
    
    print("✓ StateManager upgrade adds all required fields and methods")
    
    # Test that the added methods work
    error_details = {
        'error_message': 'Test error',
        'returncode': 1,
        'stdout': '',
        'stderr': '',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '1',
        'script_path': '/path/to/script.py'
    }
    
    old_state.set_execution_error(error_details)
    assert old_state.execution_error_occurred == True, "Upgraded method should work"
    
    old_state.acknowledge_execution_error()
    assert old_state.execution_error_acknowledged == True, "Upgraded method should work"
    
    old_state.clear_execution_error()
    assert old_state.execution_error_occurred == False, "Upgraded method should work"
    
    print("✓ Upgraded methods work correctly")

def main():
    """Run all backward compatibility tests."""
    print("Backward Compatibility Tests for Stage 7 Error Handling")
    print("=" * 60)
    
    try:
        test_safe_functions_with_old_state()
        test_state_upgrade()
        
        print("\n" + "=" * 60)
        print("🎉 All backward compatibility tests passed!")
        print("The error handling system works with existing StateManager instances.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
