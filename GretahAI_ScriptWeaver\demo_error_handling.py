#!/usr/bin/env python3
"""
Demonstration script for Stage 7 error handling functionality.

This script demonstrates how the new error handling system works in Stage 7
by simulating different types of errors and showing the state management.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager
from datetime import datetime

def demo_test_execution_failure():
    """Demonstrate handling of test execution failure (non-zero exit code)."""
    print("\n" + "="*60)
    print("DEMO: Test Execution Failure (pytest returncode != 0)")
    print("="*60)
    
    # Create state manager
    state = StateManager()
    
    # Simulate a test case step
    state.selected_step = {'Step No': '1', 'Test Steps': 'Click login button', 'Expected Result': 'Login page appears'}
    state.generated_script_path = '/path/to/test_script.py'
    
    print(f"Initial state: error_occurred = {state.execution_error_occurred}")
    
    # Simulate test execution failure
    error_details = {
        'error_message': 'Test Case Step 1 execution failed',
        'returncode': 1,
        'stdout': 'FAILED test_login.py::test_click_login - AssertionError: Element not found',
        'stderr': 'selenium.common.exceptions.NoSuchElementException: Unable to locate element',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '1',
        'script_path': '/path/to/test_script.py'
    }
    
    # Set error state (this would happen in Stage 7)
    state.set_execution_error(error_details)
    
    print(f"After error: error_occurred = {state.execution_error_occurred}")
    print(f"Error acknowledged: {state.execution_error_acknowledged}")
    print(f"Error message: {state.execution_error_details['error_message']}")
    
    # Simulate user acknowledging error
    print("\n--- User acknowledges error ---")
    state.acknowledge_execution_error()
    print(f"Error acknowledged: {state.execution_error_acknowledged}")
    
    # Clear error state
    print("\n--- Clearing error state ---")
    state.clear_execution_error()
    print(f"After clearing: error_occurred = {state.execution_error_occurred}")

def demo_python_exception():
    """Demonstrate handling of Python exception during script execution."""
    print("\n" + "="*60)
    print("DEMO: Python Exception During Script Execution")
    print("="*60)
    
    # Create state manager
    state = StateManager()
    
    # Simulate a test case step
    state.selected_step = {'Step No': '2', 'Test Steps': 'Enter username', 'Expected Result': 'Username field filled'}
    state.generated_script_path = '/path/to/test_script.py'
    
    print(f"Initial state: error_occurred = {state.execution_error_occurred}")
    
    # Simulate Python exception
    try:
        # This would be the actual script execution that fails
        raise FileNotFoundError("Test script file not found: /path/to/test_script.py")
    except Exception as e:
        # This is what Stage 7 would do
        import traceback
        traceback_str = traceback.format_exc()
        
        error_details = {
            'error_message': f"Python exception during script execution: {str(e)}",
            'returncode': -1,
            'stdout': '',
            'stderr': traceback_str,
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'step_no': '2',
            'script_path': '/path/to/test_script.py',
            'exception_type': type(e).__name__
        }
        
        state.set_execution_error(error_details)
        
        print(f"After exception: error_occurred = {state.execution_error_occurred}")
        print(f"Exception type: {state.execution_error_details['exception_type']}")
        print(f"Error message: {state.execution_error_details['error_message']}")

def demo_workflow_pause():
    """Demonstrate how error state pauses the workflow."""
    print("\n" + "="*60)
    print("DEMO: Workflow Pause Logic")
    print("="*60)
    
    # Create state manager
    state = StateManager()
    
    # Simulate error state
    error_details = {
        'error_message': 'Test execution failed',
        'returncode': 1,
        'stdout': 'Test output',
        'stderr': 'Test error',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '3',
        'script_path': '/path/to/test_script.py'
    }
    
    state.set_execution_error(error_details)
    
    # This is the logic from Stage 7 that checks if workflow should pause
    def should_pause_workflow(state):
        return (hasattr(state, 'execution_error_occurred') and 
                state.execution_error_occurred and 
                not state.execution_error_acknowledged)
    
    print(f"Should pause workflow: {should_pause_workflow(state)}")
    
    # User acknowledges error
    state.acknowledge_execution_error()
    print(f"After acknowledgment, should pause: {should_pause_workflow(state)}")
    
    # Clear error
    state.clear_execution_error()
    print(f"After clearing, should pause: {should_pause_workflow(state)}")

def demo_state_reset():
    """Demonstrate how error state is cleared during step reset."""
    print("\n" + "="*60)
    print("DEMO: Error State Reset")
    print("="*60)
    
    # Create state manager
    state = StateManager()
    
    # Set error state
    error_details = {
        'error_message': 'Test execution failed',
        'returncode': 1,
        'stdout': 'Test output',
        'stderr': 'Test error',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '4',
        'script_path': '/path/to/test_script.py'
    }
    
    state.set_execution_error(error_details)
    print(f"Before reset: error_occurred = {state.execution_error_occurred}")
    
    # Reset step state (this happens when user changes steps)
    state.reset_step_state(confirm=True, reason="User changed to different step")
    print(f"After step reset: error_occurred = {state.execution_error_occurred}")
    print(f"Error details cleared: {len(state.execution_error_details) == 0}")

def main():
    """Run all demonstrations."""
    print("Stage 7 Error Handling Demonstration")
    print("This script shows how the new error handling system works")
    
    try:
        demo_test_execution_failure()
        demo_python_exception()
        demo_workflow_pause()
        demo_state_reset()
        
        print("\n" + "="*60)
        print("🎉 All demonstrations completed successfully!")
        print("The error handling system is working as expected.")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
