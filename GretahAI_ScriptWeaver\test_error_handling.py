#!/usr/bin/env python3
"""
Test script to verify Stage 7 error handling functionality.

This script tests the new error handling features in Stage 7:
1. Error state management in StateManager
2. Error detection and workflow pause
3. Error acknowledgment UI functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager
from datetime import datetime

def test_state_manager_error_handling():
    """Test StateManager error handling methods."""
    print("Testing StateManager error handling...")
    
    # Create a StateManager instance
    state = StateManager()
    
    # Test initial state
    assert not state.execution_error_occurred
    assert not state.execution_error_acknowledged
    assert state.execution_error_details == {}
    print("✓ Initial error state is correct")
    
    # Test setting execution error
    error_details = {
        'error_message': 'Test execution failed',
        'returncode': 1,
        'stdout': 'Test output',
        'stderr': 'Test error',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'step_no': '1',
        'script_path': '/path/to/script.py'
    }
    
    state.set_execution_error(error_details)
    assert state.execution_error_occurred
    assert not state.execution_error_acknowledged
    assert state.execution_error_details == error_details
    print("✓ Error state setting works correctly")
    
    # Test acknowledging error
    result = state.acknowledge_execution_error()
    assert result is True
    assert state.execution_error_occurred
    assert state.execution_error_acknowledged
    print("✓ Error acknowledgment works correctly")
    
    # Test clearing error
    result = state.clear_execution_error()
    assert result is True
    assert not state.execution_error_occurred
    assert not state.execution_error_acknowledged
    assert state.execution_error_details == {}
    print("✓ Error clearing works correctly")
    
    # Test reset_step_state clears error state
    state.set_execution_error(error_details)
    state.reset_step_state(confirm=True, reason="Test reset")
    assert not state.execution_error_occurred
    assert not state.execution_error_acknowledged
    assert state.execution_error_details == {}
    print("✓ Step state reset clears error state")
    
    print("All StateManager error handling tests passed! ✅")

def test_error_details_structure():
    """Test that error details have the expected structure."""
    print("\nTesting error details structure...")
    
    state = StateManager()
    
    # Test with test execution failure
    test_error_details = {
        'error_message': 'Test Case Step 1 execution failed',
        'returncode': 1,
        'stdout': 'pytest output',
        'stderr': 'assertion error',
        'timestamp': '20241128_120000',
        'step_no': '1',
        'script_path': '/path/to/test_script.py'
    }
    
    state.set_execution_error(test_error_details)
    stored_details = state.execution_error_details
    
    # Verify all required fields are present
    required_fields = ['error_message', 'returncode', 'stdout', 'stderr', 'timestamp', 'step_no', 'script_path']
    for field in required_fields:
        assert field in stored_details, f"Missing required field: {field}"
    print("✓ Test execution error details have all required fields")
    
    # Test with Python exception
    exception_error_details = {
        'error_message': 'Python exception during script execution: FileNotFoundError',
        'returncode': -1,
        'stdout': '',
        'stderr': 'Traceback (most recent call last)...',
        'timestamp': '20241128_120001',
        'step_no': '2',
        'script_path': '/path/to/test_script.py',
        'exception_type': 'FileNotFoundError'
    }
    
    state.set_execution_error(exception_error_details)
    stored_details = state.execution_error_details
    
    # Verify exception-specific fields
    assert 'exception_type' in stored_details
    assert stored_details['returncode'] == -1
    print("✓ Python exception error details have correct structure")
    
    print("All error details structure tests passed! ✅")

def main():
    """Run all tests."""
    print("Starting Stage 7 Error Handling Tests")
    print("=" * 50)
    
    try:
        test_state_manager_error_handling()
        test_error_details_structure()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed successfully!")
        print("Stage 7 error handling implementation is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
