# Stage 7 Error Handling Enhancement

## Overview

This document describes the enhanced error handling implementation in Stage 7 (Test Script Execution) of GretahAI ScriptWeaver. The new system pauses the workflow when script execution fails and requires user acknowledgment before proceeding.

## Features

### 1. Error Detection and Workflow Pause
- **Test Execution Failures**: Detects when pytest returns non-zero exit codes
- **Python Exceptions**: Catches and handles Python exceptions during script execution
- **Workflow Pause**: Automatically pauses the workflow when errors occur
- **State Management**: Uses StateManager to track error state across sessions

### 2. Error Acknowledgment UI
- **Detailed Error Display**: Shows comprehensive error information including:
  - Error summary with step number, exit code, and timestamp
  - Standard output and error streams
  - Script path and execution context
- **User Actions**: Provides three options for handling errors:
  - **Acknowledge Error & Continue**: Clear error and return to Stage 4
  - **Retry Execution**: Clear error state and retry script execution
  - **View Full Logs**: Display complete error details and script content

### 3. Error State Management
- **Persistent State**: Error state persists across page refreshes
- **Centralized Management**: All error handling goes through StateManager
- **Automatic Cleanup**: Error state is cleared when steps are reset

## Implementation Details

### StateManager Changes

#### New Fields
```python
# Error handling and workflow control
execution_error_occurred: bool = False
execution_error_acknowledged: bool = False
execution_error_details: Dict[str, Any] = field(default_factory=dict)
```

#### New Methods
- `set_execution_error(error_details)`: Set error state with detailed information
- `acknowledge_execution_error()`: Mark error as acknowledged by user
- `clear_execution_error()`: Clear error state after acknowledgment

### Stage 7 Changes

#### Error Detection
- **Test Failures**: Checks `result.returncode != 0` after pytest execution
- **Python Exceptions**: Catches exceptions in try-catch block around script execution
- **Error Details**: Captures comprehensive error information including stdout, stderr, timestamps

#### Workflow Control
- **Early Return**: Pauses workflow when errors occur and haven't been acknowledged
- **UI Display**: Shows error acknowledgment UI instead of normal Stage 7 interface
- **State Updates**: Properly updates session state and triggers reruns

#### Error Information Structure
```python
error_details = {
    'error_message': 'Human-readable error description',
    'returncode': 1,  # Exit code (-1 for Python exceptions)
    'stdout': 'Standard output from execution',
    'stderr': 'Error output from execution',
    'timestamp': '20241128_120000',
    'step_no': '1',
    'script_path': '/path/to/script.py',
    'exception_type': 'FileNotFoundError'  # For Python exceptions
}
```

## User Experience

### Normal Flow (No Errors)
1. User clicks "Run Test Script" in Stage 7
2. Script executes successfully (returncode = 0)
3. Workflow automatically advances to next step or Stage 8

### Error Flow
1. User clicks "Run Test Script" in Stage 7
2. Script execution fails (returncode != 0 or Python exception)
3. Error state is set and workflow pauses
4. Error acknowledgment UI is displayed with detailed information
5. User chooses one of three actions:
   - **Acknowledge & Continue**: Returns to Stage 4 for next step
   - **Retry Execution**: Clears error and allows retry
   - **View Full Logs**: Shows complete error details

### Error Acknowledgment UI Components

#### Error Summary Section
- Failed step number
- Exit code
- Timestamp
- Error message

#### Detailed Error Information Section
- Standard output (if any)
- Standard error (if any)
- Script path
- Complete error details (expandable)

#### Action Buttons
- **Continue Workflow**: Acknowledge error and return to test case selection
- **Retry Execution**: Clear error and retry running the script
- **View Full Logs**: Toggle display of complete execution logs

## Benefits

### For Users
- **Clear Error Information**: Detailed error reporting helps understand what went wrong
- **Flexible Response**: Multiple options for handling errors (continue, retry, investigate)
- **No Lost Progress**: Workflow state is preserved during error handling
- **Better Debugging**: Complete logs and script content available for review

### For Developers
- **Centralized Error Handling**: All error logic goes through StateManager
- **Consistent Patterns**: Follows established StateManager and UI patterns
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Maintainable Code**: Clean separation of error handling logic

## Testing

The implementation includes comprehensive tests in `test_error_handling.py`:
- StateManager error handling methods
- Error details structure validation
- State transitions and cleanup
- Integration with existing workflow

## Future Enhancements

Potential improvements for future versions:
- **Error Analytics**: Track common error patterns
- **Auto-Retry Logic**: Automatic retry for transient failures
- **Error Reporting**: Send error reports to development team
- **Recovery Suggestions**: AI-powered suggestions for fixing common errors
